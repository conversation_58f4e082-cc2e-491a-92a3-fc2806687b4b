{"name": "smartSkills", "version": "1.0.0", "type": "commonjs", "main": "index.js", "license": "MIT", "dependencies": {"@types/axios": "^0.14.4", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "chalk": "4.1.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "nodemailer": "^6.10.1", "passport-jwt": "^4.0.1", "sequelize": "^6.37.7", "strip-bom": "^5.0.0", "stripe": "^18.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/chalk": "^2.2.4", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.97", "@types/node": "^22.15.17", "@types/passport-jwt": "^4.0.1", "@types/stripe": "^8.0.417", "bcrypt": "^5.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}, "scripts": {"start": "clear && node dist/server.js", "dev": "ts-node-dev --quiet --cls --respawn --transpile-only --exit-child --watch src src/server.ts", "build": "clear && tsc"}}